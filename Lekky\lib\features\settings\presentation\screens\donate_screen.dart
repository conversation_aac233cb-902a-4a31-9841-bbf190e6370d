import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/settings_navigation_provider.dart';
import '../../../../core/utils/logger.dart';
import '../widgets/base_settings_screen.dart';

/// Donate screen
class DonateScreen extends BaseSettingsScreen {
  /// Constructor
  const DonateScreen({super.key})
      : super(
          title: 'Donation Options',
          categoryIndex: SettingsCategoryIndex.donate,
        );

  double _selectedAmount = 5.0;
  final List<double> _presetAmounts = [1.0, 2.0, 5.0, 10.0, 20.0, 50.0];

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);

    return settingsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading settings: $error'),
      ),
      data: (settings) => ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Thank you card
          Card(
            margin: const EdgeInsets.only(bottom: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.favorite, color: Colors.red),
                      SizedBox(width: 16),
                      Text(
                        'Support Lekky',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Thank you message
                  const Text(
                    'Thank you for considering a donation to support the development of Lekky. Your contribution helps us continue to improve the app and add new features.',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Donation amount card
          Card(
            margin: const EdgeInsets.only(bottom: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.attach_money, color: Colors.green),
                      SizedBox(width: 16),
                      Text(
                        'Choose Amount',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Selected amount display
                  Center(
                    child: Text(
                      '${settings.currencySymbol}${_selectedAmount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Preset amounts grid
                  GridView.count(
                    crossAxisCount: 3,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    mainAxisSpacing: 8,
                    crossAxisSpacing: 8,
                    children: _presetAmounts.map((amount) {
                      return InkWell(
                        onTap: () {
                          setState(() {
                            _selectedAmount = amount;
                          });
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          decoration: BoxDecoration(
                            color: _selectedAmount == amount
                                ? Colors.green
                                : Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              '${settings.currencySymbol}${amount.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: _selectedAmount == amount
                                    ? Colors.white
                                    : Colors.black,
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 16),

                  // Custom amount
                  const Text(
                    'Or enter a custom amount:',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Custom amount input
                  CurrencyInputField(
                    value: _selectedAmount,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedAmount = value;
                        });
                      }
                    },
                    currencySymbol: settings.currencySymbol,
                    labelText: '',
                    hintText: 'Enter amount',
                    minValue: 0.01,
                    maxValue: 1000.00,
                  ),
                ],
              ),
            ),
          ),

          // Payment methods card
          Card(
            margin: const EdgeInsets.only(bottom: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.payment, color: Colors.blue),
                      SizedBox(width: 16),
                      Text(
                        'Payment Method',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // PayPal button
                  ElevatedButton.icon(
                    onPressed: () {
                      Logger.info('DonateScreen: PayPal button pressed');
                      _showPayPalDisabledDialog(context);
                    },
                    icon: const Icon(Icons.paypal),
                    label:
                        const Text('Donate with PayPal (Temporarily Disabled)'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Credit card button (placeholder)
                  ElevatedButton.icon(
                    onPressed: () {
                      _showComingSoonDialog(context);
                    },
                    icon: const Icon(Icons.credit_card),
                    label: const Text('Donate with Card'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Note card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.info, color: Colors.amber),
                      SizedBox(width: 16),
                      Text(
                        'Note',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Note text
                  const Text(
                    'All donations are voluntary and non-refundable. Lekky is a personal project and not a registered charity. Your donation will be used to support the development and maintenance of the app.',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show PayPal disabled dialog
  void _showPayPalDisabledDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Temporarily Disabled'),
        content: const Text(
          'PayPal donations are temporarily disabled due to security concerns. Please try again later.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show coming soon dialog for credit card
  void _showComingSoonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Coming Soon'),
        content: const Text(
          'Credit card donations will be available in a future update. Please use PayPal for now.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
