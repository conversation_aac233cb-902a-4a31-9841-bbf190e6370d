import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/settings_navigation_provider.dart';
import '../../../../core/utils/notification_debug_helper.dart';
import '../../../../core/services/notification_permission_manager.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../alerts/presentation/providers/alert_state_manager_provider.dart';

/// Notification utilities screen for user-accessible notification management
class NotificationUtilitiesScreen extends ConsumerStatefulWidget {
  /// Constructor
  const NotificationUtilitiesScreen({super.key});

  @override
  ConsumerState<NotificationUtilitiesScreen> createState() =>
      _NotificationUtilitiesScreenState();
}

class _NotificationUtilitiesScreenState
    extends ConsumerState<NotificationUtilitiesScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Banner with back arrow
          GestureDetector(
            onTap: () => context.go(
                '/main-settings?expanded=${SettingsCategoryIndex.notifications}'),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF4A90E2), Color(0xFF1565C0)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: const Text(
                '← Notification Utilities',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return ref.watch(settingsProvider).when(
          data: (settings) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Notification Status Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.notifications_active,
                                  color: Colors.blue, size: 24),
                              const SizedBox(width: 16),
                              const Text(
                                'Notification Status',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'View the current status of your notification settings. Configure individual notification types in the Alerts & Notifications section.',
                            style: TextStyle(fontSize: 14),
                          ),
                          const SizedBox(height: 16),
                          _buildNotificationTypesList(settings),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Utilities Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.build,
                                  color: Colors.blue, size: 24),
                              const SizedBox(width: 16),
                              const Text(
                                'Utilities',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Manage notification permissions and data.',
                            style: TextStyle(fontSize: 14),
                          ),
                          const SizedBox(height: 16),

                          // Request Permissions button
                          LekkyButton(
                            text: 'Request Permissions',
                            type: LekkyButtonType.primary,
                            size: LekkyButtonSize.fullWidth,
                            onPressed: _isLoading ? null : _requestPermissions,
                            isLoading: _isLoading,
                          ),

                          const SizedBox(height: 8),

                          // Clear All Data button
                          LekkyButton(
                            text: 'Clear All Notification Data',
                            type: LekkyButtonType.destructive,
                            size: LekkyButtonSize.fullWidth,
                            onPressed: _isLoading ? null : _clearAllData,
                            isLoading: _isLoading,
                          ),

                          const SizedBox(height: 12),

                          // Reset Alert States button
                          LekkyButton(
                            text: 'Reset All Alert States',
                            type: LekkyButtonType.secondary,
                            size: LekkyButtonSize.fullWidth,
                            onPressed: _isLoading ? null : _resetAlertStates,
                            isLoading: _isLoading,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, _) => Center(
            child: Text('Error loading settings: $error'),
          ),
        );
  }

  /// Build notification types list
  Widget _buildNotificationTypesList(dynamic settings) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Available Notification Types:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildNotificationTypeRow(
            Icons.battery_alert,
            'Low Balance Alerts',
            settings.lowBalanceAlertsEnabled ? 'Enabled' : 'Disabled',
            settings.lowBalanceAlertsEnabled,
          ),
          const SizedBox(height: 4),
          _buildNotificationTypeRow(
            Icons.schedule,
            'Time to Top-Up Alerts',
            settings.timeToTopUpAlertsEnabled ? 'Enabled' : 'Disabled',
            settings.timeToTopUpAlertsEnabled,
          ),
          const SizedBox(height: 4),
          _buildNotificationTypeRow(
            Icons.error,
            'Invalid Record Alerts',
            settings.invalidRecordAlertsEnabled ? 'Enabled' : 'Disabled',
            settings.invalidRecordAlertsEnabled,
          ),
          const SizedBox(height: 4),
          _buildNotificationTypeRow(
            Icons.alarm,
            'Meter Reminders',
            settings.remindersEnabled ? 'Enabled' : 'Disabled',
            settings.remindersEnabled,
          ),
        ],
      ),
    );
  }

  /// Build notification type row
  Widget _buildNotificationTypeRow(
    IconData icon,
    String title,
    String status,
    bool isEnabled,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isEnabled ? Colors.green : Colors.grey,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Text(
          status,
          style: TextStyle(
            fontSize: 12,
            color: isEnabled ? Colors.green : Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final permissionManager = NotificationPermissionManager();
      final granted = await permissionManager.requestPermission(context);

      // For Android 8.0.0 and similar versions, also check battery optimization
      bool batteryOptimizationHandled = true;
      try {
        // Show additional guidance for Android 8.0.0 users about battery optimization
        if (mounted) {
          await _showBatteryOptimizationGuidance();
        }
      } catch (e) {
        batteryOptimizationHandled = false;
      }

      if (mounted) {
        String message;
        Color backgroundColor;

        if (granted) {
          message = batteryOptimizationHandled
              ? 'Permissions granted. Check battery optimization settings if notifications don\'t work.'
              : 'Notification permissions granted';
          backgroundColor = Colors.green;
        } else {
          message = 'Notification permissions denied';
          backgroundColor = Colors.red;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Clear all notification data
  Future<void> _clearAllData() async {
    // Show Lekky-style confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => _buildClearAllDataDialog(context),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final debugHelper = NotificationDebugHelper();
      await debugHelper.clearAllNotificationData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All notification data cleared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Reset all time-based alert states (low balance, time to top-up, reminders)
  Future<void> _resetAlertStates() async {
    // Show Lekky-style confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => _buildResetAlertStatesDialog(context),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Use AlertStateManager to reset time-based alerts
      final alertStateManager = ref.read(alertStateManagerProvider.notifier);
      await alertStateManager.resetAllTimeBasedAlerts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Alert states reset successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error resetting alert states: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Build Lekky-style reset alert states dialog
  Widget _buildResetAlertStatesDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildResetDialogHeader(context),
            const SizedBox(height: 16),
            _buildResetWarningText(context),
            const SizedBox(height: 24),
            _buildResetInfoSummary(context),
            const SizedBox(height: 24),
            _buildResetButtonBar(context),
          ],
        ),
      ),
    );
  }

  /// Build dialog header with reset icon
  Widget _buildResetDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.refresh,
          color: theme.colorScheme.primary,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Reset All Alert States',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: () => context.pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build warning text
  Widget _buildResetWarningText(BuildContext context) {
    final theme = Theme.of(context);

    return Text(
      'This will reset the timing for low balance alerts, time to top-up alerts, and meter reminders. They will be able to fire again immediately if conditions are met.',
      style: TextStyle(
        fontSize: 16,
        color: theme.colorScheme.onSurface,
        height: 1.4,
      ),
    );
  }

  /// Build info summary showing what will be reset
  Widget _buildResetInfoSummary(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Alert states to be reset:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildResetInfoRow(
              context, Icons.battery_alert, 'Low Balance Alerts'),
          const SizedBox(height: 8),
          _buildResetInfoRow(context, Icons.schedule, 'Time to Top-up Alerts'),
          const SizedBox(height: 8),
          _buildResetInfoRow(context, Icons.notifications, 'Meter Reminders'),
          const SizedBox(height: 12),
          Text(
            'Note: Invalid entry alerts are not affected as they are not time-based.',
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual reset info row
  Widget _buildResetInfoRow(BuildContext context, IconData icon, String label) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 18,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  /// Build button bar with Cancel and Reset buttons
  Widget _buildResetButtonBar(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => context.pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Reset',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            onPressed: () => context.pop(),
          ),
        ),
      ],
    );
  }

  /// Build Lekky-style clear all notification data dialog
  Widget _buildClearAllDataDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildClearDataDialogHeader(context),
            const SizedBox(height: 16),
            _buildClearDataWarningText(context),
            const SizedBox(height: 24),
            _buildClearDataButtonBar(context),
          ],
        ),
      ),
    );
  }

  /// Build clear data dialog header with warning icon
  Widget _buildClearDataDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        const Icon(
          Icons.warning,
          color: Colors.red,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Clear All Notification Data',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: () => context.pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build clear data warning text
  Widget _buildClearDataWarningText(BuildContext context) {
    final theme = Theme.of(context);

    return Text(
      'This will clear all notification settings, history, and cached data. This action cannot be undone.',
      style: TextStyle(
        fontSize: 16,
        color: theme.colorScheme.onSurface,
        height: 1.4,
      ),
    );
  }

  /// Build clear data button bar with Cancel and Clear All buttons
  Widget _buildClearDataButtonBar(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => context.pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Clear All',
            type: LekkyButtonType.destructive,
            size: LekkyButtonSize.compact,
            onPressed: () => context.pop(),
          ),
        ),
      ],
    );
  }

  /// Show battery optimization guidance for Android 8.0.0 and similar versions
  Future<void> _showBatteryOptimizationGuidance() async {
    await showDialog(
      context: context,
      builder: (context) => _buildBatteryOptimizationDialog(context),
    );
  }

  /// Build battery optimization guidance dialog
  Widget _buildBatteryOptimizationDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBatteryOptimizationHeader(context),
            const SizedBox(height: 16),
            _buildBatteryOptimizationContent(context),
            const SizedBox(height: 24),
            _buildBatteryOptimizationButtons(context),
          ],
        ),
      ),
    );
  }

  /// Build battery optimization dialog header
  Widget _buildBatteryOptimizationHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        const Icon(
          Icons.battery_alert,
          color: Colors.orange,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Battery Optimization',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: () => context.pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build battery optimization content
  Widget _buildBatteryOptimizationContent(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'For reliable notifications on Android 8.0+, you may need to disable battery optimization for Lekky.',
          style: TextStyle(
            fontSize: 16,
            color: theme.colorScheme.onSurface,
            height: 1.4,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.orange.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Steps to disable battery optimization:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '1. Go to Settings > Apps > Lekky\n'
                '2. Tap "Battery" or "Battery Usage"\n'
                '3. Select "Don\'t optimize" or "Allow background activity"\n'
                '4. Confirm the change',
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurface,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build battery optimization dialog buttons
  Widget _buildBatteryOptimizationButtons(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Got it',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            onPressed: () => context.pop(),
          ),
        ),
      ],
    );
  }
}
